"use strict";
const common_vendor = require("../../common/vendor.js");
const common_assets = require("../../common/assets.js");
const request_api = require("../../request/api.js");
if (!Array) {
  const _component_CustomNavbar = common_vendor.resolveComponent("CustomNavbar");
  _component_CustomNavbar();
}
if (!Math) {
  (CustumTabbar + WeixinComponent + AgreementComponent)();
}
const WeixinComponent = () => "../../components/weixin/index.js";
const CustumTabbar = () => "../../components/custum-tabbar/index.js";
const AgreementComponent = () => "../../components/agreement/index.js";
const _sfc_main = {
  __name: "mine",
  setup(__props) {
    const weixinComponentRef = common_vendor.ref(null);
    const agreementComponentRef = common_vendor.ref(null);
    const hiuyuanList = common_vendor.ref([]);
    const selectedVipIndex = common_vendor.ref(0);
    const agree = common_vendor.ref(false);
    const shoplist = () => {
      request_api.MemberList().then((res) => {
        hiuyuanList.value = res.list;
        console.log("获取的会员列表", res.list);
      });
    };
    const selectVip = (index) => {
      selectedVipIndex.value = index;
      console.log("选中会员:", hiuyuanList.value[index]);
    };
    const handleVipPurchase = () => {
      if (selectedVipIndex.value === null || hiuyuanList.value.length === 0) {
        common_vendor.index.showToast({
          title: "请先选择会员套餐",
          icon: "none"
        });
        return;
      }
      if (!agree.value) {
        common_vendor.index.showToast({
          title: "请先阅读并同意服务协议",
          icon: "none"
        });
        return;
      }
      const selectedVip = hiuyuanList.value[selectedVipIndex.value];
      console.log("准备购买会员:", selectedVip);
      common_vendor.index.showToast({
        title: `准备购买${selectedVip.name}`,
        icon: "success"
      });
    };
    const userAvatar = common_vendor.ref("/static/mine/avatar.jpg");
    const userNickname = common_vendor.ref("点击登录");
    const userId = common_vendor.ref("");
    const isLoggedIn = common_vendor.ref(false);
    const userlist = async () => {
      const res = await request_api.userinfo();
      if (res && res.code === 200 && res.data) {
        const data = res.data;
        userAvatar.value = data.avatar || "/static/mine/avatar.jpg";
        userNickname.value = data.nickname || "未设置昵称";
        userId.value = data.id ? `ID:${data.id}` : "";
        isLoggedIn.value = true;
      } else {
        isLoggedIn.value = false;
        userAvatar.value = "/static/mine/avatar.jpg";
        userNickname.value = "点击登录";
        userId.value = "";
      }
    };
    common_vendor.onShow(() => {
      userlist();
      shoplist();
    });
    common_vendor.onMounted(() => {
      userlist();
    });
    const openWeixinComponent = () => {
      if (weixinComponentRef.value) {
        weixinComponentRef.value.open();
      }
    };
    const handleWeixinClose = (loggedIn) => {
      console.log("微信组件已关闭", loggedIn ? "已登录" : "");
      if (loggedIn) {
        userlist();
      }
    };
    const openMemberAgreement = () => {
      if (agreementComponentRef.value) {
        agreementComponentRef.value.openAgreement("member");
      }
    };
    const openPaymentAgreement = () => {
      if (agreementComponentRef.value) {
        agreementComponentRef.value.openAgreement("payment");
      }
    };
    const handleAgreementRead = () => {
      agree.value = true;
      console.log("用户已阅读协议，自动勾选同意按钮");
    };
    const handleMenuClick = (type) => {
      switch (type) {
        case "order":
          console.log("点击订单");
          common_vendor.index.showToast({
            title: "订单功能开发中",
            icon: "none"
          });
          break;
        case "language":
          console.log("点击语言设置");
          common_vendor.index.showToast({
            title: "语言设置功能开发中",
            icon: "none"
          });
          break;
        case "share":
          console.log("点击分享");
          common_vendor.index.showToast({
            title: "分享功能开发中",
            icon: "none"
          });
          break;
        case "agreement":
          console.log("点击用户协议");
          common_vendor.index.showToast({
            title: "用户协议功能开发中",
            icon: "none"
          });
          break;
        case "about":
          console.log("点击关于");
          common_vendor.index.showToast({
            title: "关于功能开发中",
            icon: "none"
          });
          break;
      }
    };
    return (_ctx, _cache) => {
      var _a;
      return common_vendor.e({
        a: common_vendor.p({
          title: "我的",
          changeLang: true
        }),
        b: isLoggedIn.value
      }, isLoggedIn.value ? {
        c: userAvatar.value,
        d: common_vendor.t(userNickname.value),
        e: common_vendor.t(userId.value)
      } : {
        f: common_assets._imports_0$1,
        g: common_vendor.o(openWeixinComponent)
      }, {
        h: common_assets._imports_1$1,
        i: common_vendor.f(hiuyuanList.value, (item, index, i0) => {
          return common_vendor.e({
            a: common_vendor.t(item.name),
            b: common_vendor.t(item.exceed_fee_desc),
            c: selectedVipIndex.value === index ? "#FF410A" : "#000",
            d: common_vendor.t(item.price),
            e: selectedVipIndex.value === index ? "#FF410A" : "#000",
            f: common_vendor.t(item.original_price),
            g: selectedVipIndex.value === index && item.name && item.name.includes("年")
          }, selectedVipIndex.value === index && item.name && item.name.includes("年") ? {} : {}, {
            h: index,
            i: common_vendor.o(($event) => selectVip(index), index),
            j: selectedVipIndex.value === index && item.name && item.name.includes("年") ? "150px" : "128.929px",
            k: selectedVipIndex.value === index ? "2px solid #006397" : "2px solid transparent"
          });
        }),
        j: common_vendor.t(selectedVipIndex.value !== null ? `￥${(_a = hiuyuanList.value[selectedVipIndex.value]) == null ? void 0 : _a.price} ` : ""),
        k: common_vendor.o(handleVipPurchase),
        l: agree.value,
        m: common_vendor.o(($event) => agree.value = !agree.value),
        n: common_vendor.o(openMemberAgreement),
        o: common_vendor.o(openPaymentAgreement),
        p: common_vendor.o(($event) => handleMenuClick("order")),
        q: common_vendor.o(($event) => handleMenuClick("language")),
        r: common_vendor.o(($event) => handleMenuClick("share")),
        s: common_vendor.o(($event) => handleMenuClick("agreement")),
        t: common_vendor.o(($event) => handleMenuClick("about")),
        v: common_vendor.p({
          current: "mine"
        }),
        w: common_vendor.sr(weixinComponentRef, "7c2ebfa5-2", {
          "k": "weixinComponentRef"
        }),
        x: common_vendor.o(handleWeixinClose),
        y: common_vendor.sr(agreementComponentRef, "7c2ebfa5-3", {
          "k": "agreementComponentRef"
        }),
        z: common_vendor.o(handleAgreementRead)
      });
    };
  }
};
const MiniProgramPage = /* @__PURE__ */ common_vendor._export_sfc(_sfc_main, [["__scopeId", "data-v-7c2ebfa5"]]);
wx.createPage(MiniProgramPage);
