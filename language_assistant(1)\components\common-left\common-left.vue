<template>
	<!-- 详情页导航栏 -->
	<view class="page-container">
		<view class="header" :style="{ paddingTop: statusBarHeight + 'px' }">
			<view class="section">
				<uni-icons type="left" class="uni-left" size="24" color="#006397" @click="handleClick"></uni-icons>
				<view class="text" :class="appStores.lang == 'zh-Ug' ? 'ug' : ''">{{ title }}</view>
			</view>
		</view>
	</view>
</template>

<script setup>
	import {
		ref
	} from 'vue';
	import {
		getStatusBarHeight
	} from '@/utils/system.js';
	import {
		useI18n
	} from 'vue-i18n';
	import {
		appStore
	} from '@/stores/app.js'

	const appStores = appStore()

	const {
		t,
		locale
	} = useI18n();
	const statusBarHeight = getStatusBarHeight(); // 获取状态栏高度	

	// 动态设置 props 的默认值
	const props = defineProps({
		title: {
			type: String,
			default: 'AI'
		}
	});

	// // 设置默认值
	// if (!props.title) {
	//   props.title = t('page.index.title'); // 如果没有传入 title，使用国际化的默认值
	// }

	const handleClick = () => {
		uni.navigateBack({});
	};
</script>

<style lang="scss" scoped>
	.page-container {
		position: fixed;
		top: 0;
		left: 0;
		right: 0;
		margin: 0 36rpx;
		/* z-index: 99; */
		background-color: #ffffff;
	}

	.section {
		margin: 0 13rpx;
		background-color: #F1F4F9;
		border-radius: 36rpx;
		display: flex;
		justify-content: center;
		align-items: center;
		height: 96rpx;
	}

	.uni-left {
		display: flex;
		position: relative;
		right: 60px;
	}

	.text {
		width: 140px;
		color: #006397;
		font-size: 18px;
		line-height: 33.26rpx;
		text-align: center;
		margin-right: 20px;
	}
</style>