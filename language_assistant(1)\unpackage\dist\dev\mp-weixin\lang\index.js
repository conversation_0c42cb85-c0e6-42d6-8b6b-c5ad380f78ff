"use strict";
const common_vendor = require("../common/vendor.js");
const tabbar$1 = {
  shouye: "首页",
  shoucang: "收藏",
  wode: "我的"
};
const page$1 = {
  setLanguage: {
    zh: "中",
    ug: "ئۇ"
  }
};
const home$1 = {
  usedRecords: "我用过的记录",
  wordLearning: "查词学习",
  aiLanguageAssistant: "AI语言助手",
  trainStationConversation: "在火车站买火车票时对话学习",
  hello: "我的记录"
};
const detailsPage$1 = {
  title: "语言助手",
  location: "定位",
  scene: "场景",
  startTraining: "开始针对场景训练",
  studentParent: "学生家长",
  teacher: "教师",
  student: "学生",
  parentMeeting: "家长会",
  classroom: "课堂",
  teachingResearch: "教研会",
  inputMessage: "请输入消息",
  holdToSpeak: "按住 说话",
  send: "发送",
  switchDialog: "换对话",
  adjustDifficulty: "调整难度",
  vocabularyExplanation: "词汇讲解",
  grammarExplanation: "语法讲解",
  hello: "您好",
  iAmGood: "我好",
  goodMorning: "早上好",
  releaseToSend: "松开 发送",
  releaseToCancel: "松开 取消",
  slideUpToCancel: "上滑取消录音",
  recordCanceled: "录音已取消"
};
const collection$1 = {
  title: "我的收藏",
  tabs: {
    dialogue: "收藏对话",
    courseware: "收藏课件"
  },
  content: {
    trainStationDialogue: "在火车站怎么买票对话学习",
    audioTextDialogue: "音频文字对话"
  },
  noData: {
    dialogue: "暂无收藏对话",
    courseware: "暂无收藏课件"
  },
  loading: {
    text: "加载中...",
    noMore: "没有更多数据了"
  }
};
const zhHans = {
  tabbar: tabbar$1,
  page: page$1,
  home: home$1,
  detailsPage: detailsPage$1,
  collection: collection$1
};
const tabbar = {
  shouye: "باش بەت",
  shoucang: "ساقلاش",
  wode: "مېنېڭ"
};
const page = {
  setLanguage: {
    zh: "中",
    ug: "ئۇ"
  }
};
const home = {
  usedRecords: "مەن ئىشلەتكەن خاتىرىلەر",
  wordLearning: "سۆز ئىزدەش ۋە ئۆگىنىش",
  aiLanguageAssistant: "AI تىل ياردەمچىسى",
  trainStationConversation: "پويىز ئىستانسىسىدا پويىز بېلىتى سېتىۋېلىش سۆھبىتى ئۆگىنىشى",
  hello: "ئاتانىلار"
};
const detailsPage = {
  title: "تىل ياردەمچىسى",
  location: "ئورۇن",
  scene: "مەنزىرە",
  startTraining: "مەنزىرىگە ئاساسەن مەشىق باشلاش",
  studentParent: "ئوقۇغۇچى ئاتا-ئانىلىرى",
  teacher: "ئوقۇتقۇچى",
  student: "ئوقۇغۇچى",
  parentMeeting: "ئاتا-ئانىلار يىغىنى",
  classroom: "سىنىپ",
  teachingResearch: "ئوقۇتۇش تەتقىقاتى يىغىنى",
  inputMessage: "ئۇچۇر كىرگۈزۈڭ",
  holdToSpeak: "بېسىپ سۆزلەڭ",
  send: "يوللاش",
  switchDialog: "پاراڭنى ئالماشتۇرۇش",
  adjustDifficulty: "قىيىنلىقنى تەڭشەش",
  vocabularyExplanation: "سۆز چۈشەندۈرۈش",
  grammarExplanation: "گرامماتىكا چۈشەندۈرۈش",
  hello: "ياخشىمۇسىز",
  iAmGood: "ياخشى",
  goodMorning: "ئەتىگەن خەيرلىك",
  releaseToSend: "قويۇۋېتىپ يوللاڭ",
  releaseToCancel: "قويۇۋېتىپ بىكار قىلىڭ",
  slideUpToCancel: "ئۈستىگە سۈرۈپ بىكار قىلىڭ",
  recordCanceled: "ئۈن خاتىرىلەش بىكار قىلىندى"
};
const collection = {
  title: "مېنىڭ ساقلىغانلىرىم",
  tabs: {
    dialogue: "ساقلانغان سۆھبەت",
    courseware: "ساقلانغان دەرسلىك"
  },
  content: {
    trainStationDialogue: "پويىز ئىستانسىسىدا قانداق بېلەت سېتىۋېلىش سۆھبىتى ئۆگىنىشى",
    audioTextDialogue: "ئۈن ۋە تېكىست سۆھبىتى"
  },
  noData: {
    dialogue: "ساقلانغان سۆھبەت يوق",
    courseware: "ساقلانغان دەرسلىك يوق"
  },
  loading: {
    text: "يۈكلىنىۋاتىدۇ...",
    noMore: "تېخىمۇ كۆپ سانلىق مەلۇمات يوق"
  }
};
const zhUg = {
  tabbar,
  page,
  home,
  detailsPage,
  collection
};
const messages = {
  "zh-Hans": zhHans,
  // 修改key为标准的语言代码
  "zh-Ug": zhUg
};
const i18n = common_vendor.createI18n({
  locale: common_vendor.index.getStorageSync("language") || "zh-Hans",
  // 默认语言
  messages,
  fallbackLocale: "zh-Hans"
});
exports.i18n = i18n;
