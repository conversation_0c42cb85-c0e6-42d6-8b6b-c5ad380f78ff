<custom-navbar wx:if="{{a}}" class="data-v-7c2ebfa5" u-i="7c2ebfa5-0" bind:__l="__l" u-p="{{a}}"/><view class="page-container data-v-7c2ebfa5"><view class="data-v-7c2ebfa5"><view class="user-info data-v-7c2ebfa5"><block wx:if="{{b}}"><image class="avatar data-v-7c2ebfa5" src="{{c}}"/><view class="user-detail data-v-7c2ebfa5"><text class="phone data-v-7c2ebfa5">{{d}}</text><text class="user-id data-v-7c2ebfa5">{{e}}</text></view></block><block wx:else><image class="avatar data-v-7c2ebfa5" src="{{f}}"/><view class="user-detail data-v-7c2ebfa5" bindtap="{{g}}"><text class="phone data-v-7c2ebfa5">点击登录</text></view></block></view><view class="vip-section data-v-7c2ebfa5"><view class="vip-header data-v-7c2ebfa5"><image class="vip-icon data-v-7c2ebfa5" src="{{h}}"/><text class="data-v-7c2ebfa5">开通会员享受无限制服务</text></view><view class="huiyuan data-v-7c2ebfa5"><view wx:for="{{i}}" wx:for-item="item" wx:key="h" class="data-v-7c2ebfa5" bindtap="{{item.i}}" style="{{'width:' + '106.827px' + ';' + ('height:' + item.j) + ';' + ('background:' + '#D8E6F5') + ';' + ('border-radius:' + '12rpx') + ';' + ('border:' + item.k) + ';' + ('cursor:' + 'pointer')}}"><view class="huiyuan-title data-v-7c2ebfa5"><view class="huiyuan-title-name data-v-7c2ebfa5">{{item.a}}</view><view class="huiyuan-title-desc data-v-7c2ebfa5">{{item.b}}</view><view class="huiyuan-title-xuan data-v-7c2ebfa5"><view class="huiyuan-title-xuan-flex data-v-7c2ebfa5"><view class="data-v-7c2ebfa5" style="{{'font-size:' + '20rpx' + ';' + ('margin-top:' + '4rpx') + ';' + ('color:' + item.c)}}"> ￥</view><view class="data-v-7c2ebfa5" style="{{'font-size:' + '14px' + ';' + ('color:' + item.e) + ';' + ('font-weight:' + '600') + ';' + ('margin:' + '0')}}">{{item.d}}</view></view><view class="huiyuan-title-xuan-price data-v-7c2ebfa5"><view class="data-v-7c2ebfa5">￥</view><view class="data-v-7c2ebfa5">{{item.f}}</view></view></view><view wx:if="{{item.g}}" class="huiyuan-title-pan data-v-7c2ebfa5"> 现在购买可使用13个月 </view></view></view></view><button class="vip-btn data-v-7c2ebfa5" bindtap="{{k}}">{{j}}确认协议并开通 </button><view class="vip-agree data-v-7c2ebfa5"><radio class="data-v-7c2ebfa5" checked="{{l}}" bindtap="{{m}}" color="#2a7cff" style="transform:scale(0.8)"/><text class="data-v-7c2ebfa5">开通前请阅读</text><text class="agreement-link data-v-7c2ebfa5" bindtap="{{n}}">《会员服务协议》</text><text class="data-v-7c2ebfa5">和</text><text class="agreement-link data-v-7c2ebfa5" bindtap="{{o}}">《扣费服务协议》</text></view></view><view class="menu-section data-v-7c2ebfa5"><view class="menu-item data-v-7c2ebfa5" bindtap="{{p}}"><text class="menu-text data-v-7c2ebfa5">订单</text><text class="menu-arrow data-v-7c2ebfa5">></text></view><view class="menu-item data-v-7c2ebfa5" bindtap="{{q}}"><text class="menu-text data-v-7c2ebfa5">语言设置</text><text class="menu-arrow data-v-7c2ebfa5">></text></view><view class="menu-item data-v-7c2ebfa5" bindtap="{{r}}"><text class="menu-text data-v-7c2ebfa5">分享</text><text class="menu-arrow data-v-7c2ebfa5">></text></view><view class="menu-item data-v-7c2ebfa5" bindtap="{{s}}"><text class="menu-text data-v-7c2ebfa5">用户协议</text><text class="menu-arrow data-v-7c2ebfa5">></text></view><view class="menu-item data-v-7c2ebfa5" bindtap="{{t}}"><text class="menu-text data-v-7c2ebfa5">关于</text><text class="menu-arrow data-v-7c2ebfa5">></text></view></view></view></view><custum-tabbar wx:if="{{v}}" class="data-v-7c2ebfa5" u-i="7c2ebfa5-1" bind:__l="__l" u-p="{{v}}"/><weixin-component class="r data-v-7c2ebfa5" u-r="weixinComponentRef" bindclose="{{x}}" u-i="7c2ebfa5-2" bind:__l="__l"/><agreement-component class="r data-v-7c2ebfa5" u-r="agreementComponentRef" bindagreeRead="{{z}}" u-i="7c2ebfa5-3" bind:__l="__l"/>